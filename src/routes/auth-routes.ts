/**
 * Authentication Routes (Simplified)
 *
 * Better Auth integration routes for Hono.
 * Handles basic authentication endpoints without middleware dependencies.
 * This is a temporary simplified version to avoid global scope async operations.
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { getAuth } from '../auth';

const authApp = new Hono();

// CORS configuration for auth routes
authApp.use(
  '*',
  cors({
    origin: ['http://localhost:3000', 'https://localhost:3000', 'http://localhost:8787'],
    allowHeaders: ['Content-Type', 'Authorization', 'Cookie'],
    allowMethods: ['POST', 'GET', 'OPTIONS'],
    exposeHeaders: ['Set-Cookie'],
    maxAge: 600,
    credentials: true,
  })
);

/**
 * Better Auth public endpoints (no authentication required)
 */

// Sign up endpoint
authApp.post('/sign-up/email', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Sign-up error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Registration failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Sign in endpoint
authApp.post('/sign-in/email', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Sign-in error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Login failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Sign out endpoint
authApp.post('/sign-out', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Sign-out error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Logout failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Get session endpoint (public, but returns different data based on auth status)
authApp.get('/session', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Session error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Session check failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Health check for auth service
authApp.get('/health', async (c) => {
  try {
    const _auth = await getAuth();

    return c.json({
      status: 'success',
      service: 'Better Auth',
      version: '1.0.0',
      healthy: true,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Auth health check error:', error);

    return c.json(
      {
        status: 'error',
        service: 'Better Auth',
        healthy: false,
        error: 'Service unavailable',
        timestamp: new Date().toISOString(),
      },
      503
    );
  }
});

// Catch-all for other Better Auth endpoints
authApp.on(['POST', 'GET', 'DELETE'], '/*', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Auth handler error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Authentication service error',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

export { authApp };
