/**
 * Test script to check auth module initialization
 */

async function testAuthInit() {
  try {
    console.log('Testing auth module initialization...');

    // Test if we can make a request to a simple endpoint first
    const response = await fetch('http://localhost:8787/');
    const data = await response.json();
    console.log('✅ Server is responding:', data.status);

    // Test if we can access any auth-related endpoint
    console.log('\nTesting auth endpoints...');

    // Try the catch-all auth endpoint
    const authResponse = await fetch('http://localhost:8787/api/auth', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`Auth endpoint status: ${authResponse.status} ${authResponse.statusText}`);

    const authText = await authResponse.text();
    if (authText) {
      try {
        const authData = JSON.parse(authText);
        console.log('Auth response:', JSON.stringify(authData, null, 2));
      } catch (_e) {
        console.log('Auth raw response:', authText);
      }
    } else {
      console.log('Empty auth response');
    }

    // Test a specific auth endpoint
    console.log('\nTesting specific auth endpoint...');
    const healthResponse = await fetch('http://localhost:8787/api/auth/health', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`Health endpoint status: ${healthResponse.status} ${healthResponse.statusText}`);

    const healthText = await healthResponse.text();
    if (healthText) {
      try {
        const healthData = JSON.parse(healthText);
        console.log('Health response:', JSON.stringify(healthData, null, 2));
      } catch (_e) {
        console.log('Health raw response:', healthText);
      }
    } else {
      console.log('Empty health response');
    }

    // Test other routes to see if they work
    console.log('\nTesting other routes for comparison...');

    const docResponse = await fetch('http://localhost:8787/api/documents', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`Documents endpoint status: ${docResponse.status} ${docResponse.statusText}`);

    const searchResponse = await fetch('http://localhost:8787/api/search', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`Search endpoint status: ${searchResponse.status} ${searchResponse.statusText}`);
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testAuthInit();
